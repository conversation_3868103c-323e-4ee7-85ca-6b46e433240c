import { useEffect, useRef, useState } from 'react';
import { WebSocketService } from '@/services/websocket';

export function useWebSocket(userId: string) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const wsServiceRef = useRef<WebSocketService | null>(null);

  useEffect(() => {
    if (userId) {
      wsServiceRef.current = new WebSocketService(userId);
      
      wsServiceRef.current.on('connected', () => {
        setIsConnected(true);
        setConnectionStatus('connected');
      });

      wsServiceRef.current.on('disconnected', () => {
        setIsConnected(false);
        setConnectionStatus('disconnected');
      });

      wsServiceRef.current.on('error', (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('disconnected');
      });

      setConnectionStatus('connecting');

      return () => {
        if (wsServiceRef.current) {
          wsServiceRef.current.disconnect();
          wsServiceRef.current = null;
        }
      };
    }
  }, [userId]);

  return {
    wsService: wsServiceRef.current,
    isConnected,
    connectionStatus,
  };
}