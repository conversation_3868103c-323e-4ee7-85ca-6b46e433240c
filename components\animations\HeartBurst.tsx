import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  withDelay,
  runOnJS,
} from 'react-native-reanimated';
import { Heart } from 'lucide-react-native';
import { theme } from '@/constants/theme';

interface HeartBurstProps {
  visible: boolean;
  onComplete?: () => void;
  size?: number;
  color?: string;
}

interface HeartParticle {
  id: number;
  x: number;
  y: number;
  delay: number;
  scale: number;
}

export default function HeartBurst({ 
  visible, 
  onComplete, 
  size = 60, 
  color = theme.colors.like 
}: HeartBurstProps) {
  const mainScale = useSharedValue(0);
  const mainOpacity = useSharedValue(0);
  
  // Generate random heart particles
  const particles: HeartParticle[] = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    x: Math.random() * 200 - 100, // Random x between -100 and 100
    y: Math.random() * 200 - 100, // Random y between -100 and 100
    delay: Math.random() * 200,
    scale: 0.3 + Math.random() * 0.4, // Random scale between 0.3 and 0.7
  }));

  useEffect(() => {
    if (visible) {
      // Main heart animation
      mainScale.value = withSequence(
        withTiming(1.3, { duration: 200 }),
        withSpring(1, { damping: 8, stiffness: 100 })
      );
      mainOpacity.value = withTiming(1, { duration: 200 });

      // Hide after animation
      setTimeout(() => {
        mainOpacity.value = withTiming(0, { duration: 300 });
        mainScale.value = withTiming(0, { duration: 300 });
        if (onComplete) {
          setTimeout(() => runOnJS(onComplete)(), 300);
        }
      }, 1500);
    } else {
      mainScale.value = 0;
      mainOpacity.value = 0;
    }
  }, [visible]);

  const mainHeartStyle = useAnimatedStyle(() => ({
    transform: [{ scale: mainScale.value }],
    opacity: mainOpacity.value,
  }));

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* Main Heart */}
      <Animated.View style={[styles.mainHeart, mainHeartStyle]}>
        <Heart size={size} color={color} fill={color} />
      </Animated.View>

      {/* Particle Hearts */}
      {particles.map((particle) => (
        <ParticleHeart
          key={particle.id}
          particle={particle}
          color={color}
          visible={visible}
        />
      ))}
    </View>
  );
}

interface ParticleHeartProps {
  particle: HeartParticle;
  color: string;
  visible: boolean;
}

function ParticleHeart({ particle, color, visible }: ParticleHeartProps) {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const rotation = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      // Delayed start for each particle
      setTimeout(() => {
        translateX.value = withTiming(particle.x, { duration: 1000 });
        translateY.value = withTiming(particle.y, { duration: 1000 });
        scale.value = withSequence(
          withTiming(particle.scale, { duration: 200 }),
          withDelay(600, withTiming(0, { duration: 200 }))
        );
        opacity.value = withSequence(
          withTiming(0.8, { duration: 200 }),
          withDelay(600, withTiming(0, { duration: 200 }))
        );
        rotation.value = withTiming(Math.random() * 360, { duration: 1000 });
      }, particle.delay);
    } else {
      translateX.value = 0;
      translateY.value = 0;
      scale.value = 0;
      opacity.value = 0;
      rotation.value = 0;
    }
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.particle, animatedStyle]}>
      <Heart size={16} color={color} fill={color} />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
    zIndex: 1000,
  },
  mainHeart: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  particle: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
