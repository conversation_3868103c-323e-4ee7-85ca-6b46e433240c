import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Heart,
  Users,
  MessageCircle,
  TrendingUp,
  Calendar,
  Download,
} from 'lucide-react-native';
import { theme } from '@/constants/theme';

const { width } = Dimensions.get('window');

interface AnalyticsData {
  period: string;
  likes: number;
  matches: number;
  messages: number;
  newUsers: number;
}

const MOCK_ANALYTICS: AnalyticsData[] = [
  { period: 'Today', likes: 2847, matches: 156, messages: 892, newUsers: 23 },
  { period: 'Yesterday', likes: 2654, matches: 142, messages: 834, newUsers: 19 },
  { period: 'This Week', likes: 18432, matches: 1089, messages: 5672, newUsers: 167 },
  { period: 'Last Week', likes: 17234, matches: 1023, messages: 5234, newUsers: 145 },
  { period: 'This Month', likes: 76543, matches: 4567, messages: 23456, newUsers: 678 },
  { period: 'Last Month', likes: 72345, matches: 4234, messages: 21234, newUsers: 634 },
];

const METRICS = [
  { key: 'likes', label: 'Likes', icon: Heart, color: theme.colors.like },
  { key: 'matches', label: 'Matches', icon: MessageCircle, color: theme.colors.primary },
  { key: 'messages', label: 'Messages', icon: MessageCircle, color: theme.colors.info },
  { key: 'newUsers', label: 'New Users', icon: Users, color: theme.colors.success },
];

export default function AnalyticsPage() {
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState('Today');

  const currentData = MOCK_ANALYTICS.find(data => data.period === selectedPeriod) || MOCK_ANALYTICS[0];
  const previousData = MOCK_ANALYTICS[MOCK_ANALYTICS.findIndex(data => data.period === selectedPeriod) + 1];

  const calculateGrowth = (current: number, previous: number) => {
    if (!previous) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const renderMetricCard = (metric: any) => {
    const currentValue = currentData[metric.key as keyof AnalyticsData] as number;
    const previousValue = previousData ? previousData[metric.key as keyof AnalyticsData] as number : 0;
    const growth = calculateGrowth(currentValue, previousValue);
    const isPositive = parseFloat(growth) >= 0;

    return (
      <View key={metric.key} style={styles.metricCard}>
        <View style={styles.metricHeader}>
          <View style={[styles.metricIcon, { backgroundColor: `${metric.color}20` }]}>
            <metric.icon size={20} color={metric.color} />
          </View>
          <View style={[styles.growthBadge, { backgroundColor: isPositive ? theme.colors.success : theme.colors.error }]}>
            <TrendingUp size={12} color="white" />
            <Text style={styles.growthText}>{growth}%</Text>
          </View>
        </View>
        <Text style={styles.metricValue}>{currentValue.toLocaleString()}</Text>
        <Text style={styles.metricLabel}>{metric.label}</Text>
      </View>
    );
  };

  const renderPeriodButton = (period: string) => (
    <TouchableOpacity
      key={period}
      style={[
        styles.periodButton,
        selectedPeriod === period && styles.selectedPeriodButton,
      ]}
      onPress={() => setSelectedPeriod(period)}
    >
      <Text
        style={[
          styles.periodButtonText,
          selectedPeriod === period && styles.selectedPeriodButtonText,
        ]}
      >
        {period}
      </Text>
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.title}>Analytics</Text>
          <TouchableOpacity style={styles.downloadButton}>
            <Download size={24} color="white" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Period Selector */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Time Period</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.periodSelector}>
              {['Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month'].map(renderPeriodButton)}
            </ScrollView>
          </View>

          {/* Metrics Grid */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Key Metrics</Text>
            <View style={styles.metricsGrid}>
              {METRICS.map(renderMetricCard)}
            </View>
          </View>

          {/* Detailed Stats */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Detailed Statistics</Text>
            <View style={styles.statsCard}>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Average Likes per User</Text>
                <Text style={styles.statValue}>
                  {(currentData.likes / Math.max(currentData.newUsers, 1)).toFixed(1)}
                </Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Match Rate</Text>
                <Text style={styles.statValue}>
                  {((currentData.matches / currentData.likes) * 100).toFixed(1)}%
                </Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Messages per Match</Text>
                <Text style={styles.statValue}>
                  {(currentData.messages / Math.max(currentData.matches, 1)).toFixed(1)}
                </Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>User Engagement</Text>
                <Text style={styles.statValue}>
                  {((currentData.messages / currentData.likes) * 100).toFixed(1)}%
                </Text>
              </View>
            </View>
          </View>

          {/* Trends */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Trends</Text>
            <View style={styles.trendsCard}>
              <Text style={styles.trendText}>
                📈 Likes are up {calculateGrowth(currentData.likes, previousValue?.likes || 0)}% compared to previous period{'\n\n'}
                💕 Match rate is {((currentData.matches / currentData.likes) * 100).toFixed(1)}%{'\n\n'}
                💬 Users are sending {(currentData.messages / Math.max(currentData.matches, 1)).toFixed(1)} messages per match on average{'\n\n'}
                👥 {currentData.newUsers} new users joined in this period
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  downloadButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 16,
  },
  periodSelector: {
    flexDirection: 'row',
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginRight: 8,
  },
  selectedPeriodButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  periodButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  selectedPeriodButtonText: {
    color: 'white',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  metricCard: {
    width: (width - 60) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    ...theme.shadows.sm,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  metricIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  growthBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  growthText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginLeft: 2,
  },
  metricValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: 'white',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    ...theme.shadows.sm,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statValue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  trendsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    ...theme.shadows.sm,
  },
  trendText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
});
