export interface User {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  lastSeen?: Date;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'voice' | 'video' | 'sticker';
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  replyTo?: string;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number; // for voice messages
}

// Likes and matching types
export interface Like {
  id: string;
  fromUserId: string;
  toUserId: string;
  timestamp: Date;
  type: 'like' | 'superlike' | 'pass';
  isMatch?: boolean;
}

export interface Match {
  id: string;
  users: [string, string]; // User IDs
  timestamp: Date;
  lastActivity?: Date;
  status: 'active' | 'expired' | 'blocked';
}

export interface LikeProfile {
  id: string;
  name: string;
  age: number;
  photos: string[];
  bio?: string;
  distance?: number;
  isMatch: boolean;
  isPremium: boolean;
  likedAt: Date;
  mutualFriends?: number;
  interests?: string[];
}

export interface Conversation {
  id: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  isTyping: boolean;
  typingUsers: string[];
}

export interface CallSession {
  id: string;
  type: 'audio' | 'video';
  participants: User[];
  status: 'incoming' | 'outgoing' | 'connected' | 'ended';
  startTime?: Date;
  endTime?: Date;
  isRecording?: boolean;
  isScreenSharing?: boolean;
}

export interface FileUpload {
  id: string;
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'failed';
}