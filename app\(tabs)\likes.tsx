import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Heart, Star, MessageCircle, Crown } from 'lucide-react-native';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;

// Mock data for likes
const LIKES_DATA = [
  {
    id: 1,
    name: '<PERSON>',
    age: 28,
    photo: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
    isMatch: false,
    isPremium: false,
  },
  {
    id: 2,
    name: '<PERSON>',
    age: 26,
    photo: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    isMatch: true,
    isPremium: false,
  },
  {
    id: 3,
    name: '<PERSON>',
    age: 30,
    photo: 'https://images.pexels.com/photos/1547971/pexels-photo-1547971.jpeg?auto=compress&cs=tinysrgb&w=400',
    isMatch: false,
    isPremium: true,
  },
];

const MATCHES_DATA = [
  {
    id: 1,
    name: 'Sophia',
    age: 26,
    photo: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    lastMessage: 'Hey! Thanks for the like 😊',
    time: '2 hours ago',
    unread: true,
  },
  {
    id: 2,
    name: 'Olivia',
    age: 24,
    photo: 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
    lastMessage: 'Would love to grab coffee sometime!',
    time: '1 day ago',
    unread: false,
  },
];

export default function LikesTab() {
  const [activeTab, setActiveTab] = useState<'likes' | 'matches'>('likes');

  const renderLikeCard = (person: any) => (
    <TouchableOpacity key={person.id} style={styles.likeCard}>
      <Image source={{ uri: person.photo }} style={styles.likeCardImage} />
      
      {person.isPremium && (
        <View style={styles.premiumBadge}>
          <Crown size={12} color="#FFD700" />
        </View>
      )}

      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.6)']}
        style={styles.likeCardGradient}
      />

      <View style={styles.likeCardInfo}>
        <Text style={styles.likeCardName}>{person.name}, {person.age}</Text>
        
        {person.isMatch ? (
          <TouchableOpacity style={styles.matchButton}>
            <MessageCircle size={16} color="white" />
            <Text style={styles.matchButtonText}>Message</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.likeBackButton}>
            <Heart size={16} color="#42DDA6" />
            <Text style={styles.likeBackButtonText}>Like Back</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderMatchCard = (match: any) => (
    <TouchableOpacity key={match.id} style={styles.matchCard}>
      <Image source={{ uri: match.photo }} style={styles.matchPhoto} />
      
      <View style={styles.matchInfo}>
        <View style={styles.matchHeader}>
          <Text style={styles.matchName}>{match.name}, {match.age}</Text>
          <Text style={styles.matchTime}>{match.time}</Text>
        </View>
        <Text style={[styles.matchMessage, match.unread && styles.unreadMessage]}>
          {match.lastMessage}
        </Text>
      </View>

      {match.unread && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <Text style={styles.title}>Likes & Matches</Text>
        </View>

        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'likes' && styles.activeTab]}
            onPress={() => setActiveTab('likes')}
          >
            <Heart
              size={20}
              color={activeTab === 'likes' ? '#8B5CF6' : 'rgba(255, 255, 255, 0.7)'}
            />
            <Text
              style={[
                styles.tabText,
                activeTab === 'likes' && styles.activeTabText,
              ]}
            >
              Likes ({LIKES_DATA.length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'matches' && styles.activeTab]}
            onPress={() => setActiveTab('matches')}
          >
            <MessageCircle
              size={20}
              color={activeTab === 'matches' ? '#8B5CF6' : 'rgba(255, 255, 255, 0.7)'}
            />
            <Text
              style={[
                styles.tabText,
                activeTab === 'matches' && styles.activeTabText,
              ]}
            >
              Matches ({MATCHES_DATA.length})
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {activeTab === 'likes' ? (
            <View style={styles.likesContainer}>
              <View style={styles.premiumBanner}>
                <Crown size={24} color="#FFD700" />
                <View style={styles.premiumBannerContent}>
                  <Text style={styles.premiumBannerTitle}>See Who Likes You</Text>
                  <Text style={styles.premiumBannerSubtitle}>
                    Upgrade to Premium to see all your likes instantly
                  </Text>
                </View>
                <TouchableOpacity style={styles.upgradeButton}>
                  <Text style={styles.upgradeButtonText}>Upgrade</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.likesGrid}>
                {LIKES_DATA.map(renderLikeCard)}
              </View>
            </View>
          ) : (
            <View style={styles.matchesContainer}>
              {MATCHES_DATA.length > 0 ? (
                MATCHES_DATA.map(renderMatchCard)
              ) : (
                <View style={styles.emptyState}>
                  <MessageCircle size={64} color="rgba(255, 255, 255, 0.5)" />
                  <Text style={styles.emptyTitle}>No matches yet</Text>
                  <Text style={styles.emptySubtitle}>
                    Start swiping to find your perfect match!
                  </Text>
                </View>
              )}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: 24,
    borderRadius: 12,
    padding: 4,
    marginBottom: 24,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  activeTab: {
    backgroundColor: 'white',
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  activeTabText: {
    color: '#8B5CF6',
  },
  content: {
    flex: 1,
  },
  likesContainer: {
    paddingHorizontal: 24,
  },
  premiumBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  premiumBannerContent: {
    flex: 1,
    marginLeft: 12,
  },
  premiumBannerTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 4,
  },
  premiumBannerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  upgradeButton: {
    backgroundColor: '#FFD700',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  upgradeButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000',
  },
  likesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    paddingBottom: 24,
  },
  likeCard: {
    width: CARD_WIDTH,
    height: CARD_WIDTH * 1.3,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  likeCardImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  premiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    padding: 6,
    zIndex: 1,
  },
  likeCardGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40%',
  },
  likeCardInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 12,
  },
  likeCardName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 8,
  },
  matchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#42DDA6',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
  },
  matchButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  likeBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
  },
  likeBackButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#42DDA6',
  },
  matchesContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  matchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    position: 'relative',
  },
  matchPhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  matchInfo: {
    flex: 1,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  matchName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  matchTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
  },
  matchMessage: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  unreadMessage: {
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#42DDA6',
    position: 'absolute',
    right: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: 'white',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
});