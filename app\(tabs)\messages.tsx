import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import ChatList from '@/components/messaging/ChatList';
import ChatScreen from '@/components/messaging/ChatScreen';
import CallScreen from '@/components/calling/CallScreen';
import IncomingCallModal from '@/components/calling/IncomingCallModal';
import { WebSocketService } from '@/services/websocket';
import { WebRTCService } from '@/services/webrtc';
import { FileUploadService } from '@/services/fileUpload';
import { EncryptionService } from '@/services/encryption';
import { Message, Conversation, User, CallSession } from '@/types/messaging';

// Mock data - replace with real data from your backend
const CURRENT_USER: User = {
  id: 'current-user',
  name: 'You',
  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
  isOnline: true,
};

const MOCK_CONVERSATIONS: Conversation[] = [
  {
    id: 'conv-1',
    participants: [
      CURRENT_USER,
      {
        id: 'user-1',
        name: 'Sophia',
        avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
        isOnline: true,
      }
    ],
    lastMessage: {
      id: 'msg-1',
      senderId: 'user-1',
      receiverId: 'current-user',
      content: 'Hey! Thanks for the like 😊',
      type: 'text',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: 'delivered',
    },
    unreadCount: 2,
    isTyping: false,
    typingUsers: [],
  },
  {
    id: 'conv-2',
    participants: [
      CURRENT_USER,
      {
        id: 'user-2',
        name: 'Olivia',
        avatar: 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
        isOnline: false,
      }
    ],
    lastMessage: {
      id: 'msg-2',
      senderId: 'current-user',
      receiverId: 'user-2',
      content: 'Would love to grab coffee sometime!',
      type: 'text',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      status: 'read',
    },
    unreadCount: 0,
    isTyping: false,
    typingUsers: [],
  },
];

const MOCK_MESSAGES: Message[] = [
  {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! How are you doing?',
    type: 'text',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-2',
    senderId: 'current-user',
    receiverId: 'user-1',
    content: 'I\'m doing great! Thanks for asking 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-3',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'That\'s awesome! Want to grab coffee this weekend?',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
];

export default function MessagesTab() {
  const router = useRouter();
  const [currentView, setCurrentView] = useState<'list' | 'chat' | 'call'>('list');
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>(MOCK_CONVERSATIONS);
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentCall, setCurrentCall] = useState<CallSession | null>(null);
  const [incomingCall, setIncomingCall] = useState<CallSession | null>(null);
  const [showIncomingCall, setShowIncomingCall] = useState(false);

  // Services
  const [wsService, setWsService] = useState<WebSocketService | null>(null);
  const [webrtcService, setWebrtcService] = useState<WebRTCService | null>(null);
  const [fileUploadService] = useState(new FileUploadService());
  const [encryptionService] = useState(new EncryptionService());

  // Call states
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isSpeakerEnabled, setIsSpeakerEnabled] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    // Initialize services
    const ws = new WebSocketService(CURRENT_USER.id);
    const webrtc = new WebRTCService();
    
    setWsService(ws);
    setWebrtcService(webrtc);

    // Setup WebSocket listeners
    ws.on('message', handleNewMessage);
    ws.on('messageStatus', handleMessageStatus);
    ws.on('typing', handleTyping);
    ws.on('callIncoming', handleIncomingCall);
    ws.on('callAnswer', handleCallAnswer);
    ws.on('callEnd', handleCallEnd);

    // Setup WebRTC listeners
    webrtc.on('localStream', setLocalStream);
    webrtc.on('remoteStream', setRemoteStream);
    webrtc.on('offer', (offer) => ws.send({ type: 'callOffer', payload: offer }));
    webrtc.on('answer', (answer) => ws.send({ type: 'callAnswer', payload: answer }));
    webrtc.on('iceCandidate', (candidate) => ws.send({ type: 'iceCandidate', payload: candidate }));
    webrtc.on('callEnded', () => setCurrentCall(null));

    // Initialize encryption
    encryptionService.generateKeyPair();

    return () => {
      ws.disconnect();
      webrtc.cleanup();
    };
  }, []);

  const handleNewMessage = (message: Message) => {
    setMessages(prev => [...prev, message]);
    
    // Update conversation last message
    setConversations(prev => prev.map(conv => {
      if (conv.participants.some(p => p.id === message.senderId)) {
        return {
          ...conv,
          lastMessage: message,
          unreadCount: message.receiverId === CURRENT_USER.id ? conv.unreadCount + 1 : conv.unreadCount,
        };
      }
      return conv;
    }));
  };

  const handleMessageStatus = (data: { messageId: string; status: string }) => {
    setMessages(prev => prev.map(msg => 
      msg.id === data.messageId ? { ...msg, status: data.status as any } : msg
    ));
  };

  const handleTyping = (data: { conversationId: string; userId: string; isTyping: boolean }) => {
    setConversations(prev => prev.map(conv => {
      if (conv.id === data.conversationId) {
        const typingUsers = data.isTyping 
          ? [...conv.typingUsers, data.userId]
          : conv.typingUsers.filter(id => id !== data.userId);
        
        return {
          ...conv,
          isTyping: typingUsers.length > 0,
          typingUsers,
        };
      }
      return conv;
    }));
  };

  const handleIncomingCall = (callSession: CallSession) => {
    setIncomingCall(callSession);
    setShowIncomingCall(true);
  };

  const handleCallAnswer = (data: any) => {
    webrtcService?.handleAnswer(data.answer);
  };

  const handleCallEnd = () => {
    setCurrentCall(null);
    setCurrentView('list');
    webrtcService?.endCall();
  };

  const handleConversationSelect = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    setCurrentView('chat');
    
    // Load messages for this conversation
    setMessages(MOCK_MESSAGES); // Replace with actual API call
    
    // Mark messages as read
    const unreadMessages = MOCK_MESSAGES.filter(m => 
      m.receiverId === CURRENT_USER.id && m.status !== 'read'
    );
    unreadMessages.forEach(msg => wsService?.markAsRead(msg.id));
  };

  const handleSendMessage = async (content: string, type: 'text' | 'image' | 'file') => {
    if (!selectedConversation || !wsService) return;

    const otherUser = selectedConversation.participants.find(p => p.id !== CURRENT_USER.id);
    if (!otherUser) return;

    const message: Partial<Message> = {
      id: Date.now().toString(),
      senderId: CURRENT_USER.id,
      receiverId: otherUser.id,
      content,
      type,
      timestamp: new Date(),
      status: 'sending',
    };

    // Add message to local state immediately
    setMessages(prev => [...prev, message as Message]);

    // Encrypt message if needed
    try {
      if (type === 'text') {
        // In production, encrypt the message content
        // const encryptedContent = await encryptionService.encryptMessage(content, otherUserPublicKey);
        // message.content = encryptedContent;
      }

      wsService.sendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Update message status to failed
      setMessages(prev => prev.map(msg => 
        msg.id === message.id ? { ...msg, status: 'sending' } : msg
      ));
    }
  };

  const handleStartCall = async (userId: string, type: 'audio' | 'video') => {
    if (!wsService || !webrtcService) return;

    const callSession: CallSession = {
      id: Date.now().toString(),
      type,
      participants: [
        CURRENT_USER,
        conversations.find(c => c.participants.some(p => p.id === userId))?.participants.find(p => p.id === userId)!
      ],
      status: 'outgoing',
      startTime: new Date(),
    };

    setCurrentCall(callSession);
    setCurrentView('call');

    try {
      await webrtcService.startCall(type);
      wsService.initiateCall(userId, type);
    } catch (error) {
      console.error('Failed to start call:', error);
      setCurrentCall(null);
      setCurrentView('list');
    }
  };

  const handleAcceptCall = async () => {
    if (!incomingCall || !webrtcService || !wsService) return;

    setCurrentCall(incomingCall);
    setShowIncomingCall(false);
    setCurrentView('call');

    try {
      // This would be the offer from the caller
      const offer = {}; // Get from call session
      await webrtcService.answerCall(offer as RTCSessionDescriptionInit);
      wsService.answerCall(incomingCall.id);
    } catch (error) {
      console.error('Failed to answer call:', error);
    }
  };

  const handleDeclineCall = () => {
    if (!incomingCall || !wsService) return;

    wsService.endCall(incomingCall.id);
    setIncomingCall(null);
    setShowIncomingCall(false);
  };

  const handleEndCall = () => {
    if (!currentCall || !wsService) return;

    wsService.endCall(currentCall.id);
    webrtcService?.endCall();
    setCurrentCall(null);
    setCurrentView('list');
  };

  const handleToggleVideo = async () => {
    if (webrtcService) {
      const enabled = await webrtcService.toggleVideo();
      setIsVideoEnabled(enabled);
    }
  };

  const handleToggleAudio = async () => {
    if (webrtcService) {
      const enabled = await webrtcService.toggleAudio();
      setIsAudioEnabled(enabled);
    }
  };

  const handleStartScreenShare = async () => {
    if (webrtcService) {
      await webrtcService.startScreenShare();
      setIsScreenSharing(true);
    }
  };

  const handleStopScreenShare = async () => {
    if (webrtcService) {
      await webrtcService.stopScreenShare();
      setIsScreenSharing(false);
    }
  };

  const handleStartRecording = () => {
    if (webrtcService) {
      webrtcService.startRecording();
      setIsRecording(true);
    }
  };

  const handleStopRecording = () => {
    if (webrtcService) {
      webrtcService.stopRecording();
      setIsRecording(false);
    }
  };

  const handleStartTyping = () => {
    if (selectedConversation && wsService) {
      wsService.startTyping(selectedConversation.id);
    }
  };

  const handleStopTyping = () => {
    if (selectedConversation && wsService) {
      wsService.stopTyping(selectedConversation.id);
    }
  };

  const handleMarkAsRead = (messageId: string) => {
    wsService?.markAsRead(messageId);
  };

  if (currentView === 'call' && currentCall) {
    return (
      <CallScreen
        callSession={currentCall}
        currentUser={CURRENT_USER}
        localStream={localStream || undefined}
        remoteStream={remoteStream || undefined}
        onEndCall={handleEndCall}
        onToggleVideo={handleToggleVideo}
        onToggleAudio={handleToggleAudio}
        onToggleSpeaker={() => setIsSpeakerEnabled(!isSpeakerEnabled)}
        onStartScreenShare={handleStartScreenShare}
        onStopScreenShare={handleStopScreenShare}
        onStartRecording={handleStartRecording}
        onStopRecording={handleStopRecording}
        isVideoEnabled={isVideoEnabled}
        isAudioEnabled={isAudioEnabled}
        isSpeakerEnabled={isSpeakerEnabled}
        isScreenSharing={isScreenSharing}
        isRecording={isRecording}
      />
    );
  }

  if (currentView === 'chat' && selectedConversation) {
    return (
      <View style={styles.container}>
        <ChatScreen
          conversation={selectedConversation}
          messages={messages}
          currentUser={CURRENT_USER}
          onSendMessage={handleSendMessage}
          onStartCall={(type) => {
            const otherUser = selectedConversation.participants.find(p => p.id !== CURRENT_USER.id);
            if (otherUser) {
              handleStartCall(otherUser.id, type);
            }
          }}
          onGoBack={() => setCurrentView('list')}
          onStartTyping={handleStartTyping}
          onStopTyping={handleStopTyping}
          onMarkAsRead={handleMarkAsRead}
        />
        
        {incomingCall && (
          <IncomingCallModal
            visible={showIncomingCall}
            callSession={incomingCall}
            caller={incomingCall.participants.find(p => p.id !== CURRENT_USER.id) || incomingCall.participants[0]}
            onAccept={handleAcceptCall}
            onDecline={handleDeclineCall}
            onSendMessage={() => {
              // Handle quick message
              setShowIncomingCall(false);
            }}
          />
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ChatList
        conversations={conversations}
        currentUser={CURRENT_USER}
        onConversationSelect={handleConversationSelect}
        onStartCall={handleStartCall}
      />
      
      {incomingCall && (
        <IncomingCallModal
          visible={showIncomingCall}
          callSession={incomingCall}
          caller={incomingCall.participants.find(p => p.id !== CURRENT_USER.id) || incomingCall.participants[0]}
          onAccept={handleAcceptCall}
          onDecline={handleDeclineCall}
          onSendMessage={() => {
            // Handle quick message
            setShowIncomingCall(false);
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});